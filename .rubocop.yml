# Omakase Ruby styling for Rails
inherit_gem: { rubocop-rails-omakase: rubocop.yml }

# Exclude ERB templates from Ruby linting
AllCops:
  Exclude:
    - 'app/views/**/*.html.erb'
    - 'app/views/**/*.text.erb'
    - 'app/views/**/*.json.erb'
    - 'app/views/**/*.xml.erb'

# Overwrite or add rules to create your own house style
#
# # Use `[a, [b, c]]` not `[ a, [ b, c ] ]`
# Layout/SpaceInsideArrayLiteralBrackets:
#   Enabled: false
