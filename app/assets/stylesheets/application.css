/*
 * This is a manifest file that'll be compiled into application.css.
 *
 * With Propshaft, assets are served efficiently without preprocessing steps. You can still include
 * application-wide styles in this file, but keep in mind that CSS precedence will follow the standard
 * cascading order, meaning styles declared later in the document or manifest will override earlier ones,
 * depending on specificity.
 *
 * Consider organizing styles into separate files for maintainability.
 */

/* Base styles */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  margin: 0;
  padding: 0;
  background-color: #f8f9fa;
  color: #333;
}

/* Home page styles */
.home-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.home-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.home-header h1 {
  margin: 0;
  color: #333;
}

.user-info {
  text-align: right;
}

.user-info p {
  margin: 0 0 10px 0;
  color: #666;
}

.home-content {
  background: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.welcome-message h2 {
  color: #333;
  margin-bottom: 15px;
}

.mfa-status {
  margin-top: 30px;
  padding: 15px;
  border-radius: 4px;
}

.text-success {
  color: #155724;
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
}

.text-warning {
  color: #856404;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
}

/* Flash messages */
.notice, .alert {
  padding: 12px 20px;
  margin: 10px 0;
  border-radius: 4px;
}

.notice {
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
}

.alert {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
}
