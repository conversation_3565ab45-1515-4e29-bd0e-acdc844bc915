/*
 * This is a manifest file that'll be compiled into application.css.
 *
 * With Propshaft, assets are served efficiently without preprocessing steps. You can still include
 * application-wide styles in this file, but keep in mind that CSS precedence will follow the standard
 * cascading order, meaning styles declared later in the document or manifest will override earlier ones,
 * depending on specificity.
 *
 * Consider organizing styles into separate files for maintainability.
 */

/* Base styles */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  margin: 0;
  padding: 0;
  background-color: #f8f9fa;
  color: #333;
}

/* Authentication styles */
.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
}

.auth-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 400px;
}

.auth-card h1 {
  text-align: center;
  margin-bottom: 30px;
  color: #333;
}

.auth-form {
  width: 100%;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #555;
}

.form-control {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
  box-sizing: border-box;
}

.form-control:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.form-actions {
  margin-top: 30px;
}

.btn {
  display: inline-block;
  padding: 12px 24px;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-primary {
  background-color: #007bff;
  color: white;
  width: 100%;
}

.btn-primary:hover {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #545b62;
}

.btn-warning {
  background-color: #ffc107;
  color: #212529;
}

.btn-warning:hover {
  background-color: #e0a800;
}

.auth-links {
  text-align: center;
  margin-top: 20px;
}

.auth-links a {
  color: #007bff;
  text-decoration: none;
}

.auth-links a:hover {
  text-decoration: underline;
}

/* Alert styles */
.alert {
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.alert-danger {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
}

.alert ul {
  margin: 0;
  padding-left: 20px;
}

/* MFA styles */
.mfa-setup {
  text-align: left;
}

.mfa-setup ul {
  margin: 10px 0;
  padding-left: 20px;
}

.mfa-setup ul li {
  margin: 5px 0;
}

.qr-code {
  margin: 20px 0;
  display: flex;
  justify-content: center;
  padding: 20px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
}

.qr-code svg {
  max-width: 200px;
  height: auto;
}

.manual-setup {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  margin: 15px 0;
}

.manual-setup p {
  margin: 8px 0;
}

.manual-setup code {
  background: #e9ecef;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: monospace;
}

details {
  margin: 20px 0;
  padding: 10px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
}

summary {
  cursor: pointer;
  font-weight: bold;
  margin-bottom: 10px;
}

/* Home page styles */
.home-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.home-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.home-header h1 {
  margin: 0;
  color: #333;
}

.user-info {
  text-align: right;
}

.user-info p {
  margin: 0 0 10px 0;
  color: #666;
}

.home-content {
  background: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.welcome-message h2 {
  color: #333;
  margin-bottom: 15px;
}

.mfa-status {
  margin-top: 30px;
  padding: 15px;
  border-radius: 4px;
}

.text-success {
  color: #155724;
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
}

.text-warning {
  color: #856404;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
}

/* Flash messages */
.notice, .alert {
  padding: 12px 20px;
  margin: 10px 0;
  border-radius: 4px;
}

.notice {
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
}

.alert {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
}
