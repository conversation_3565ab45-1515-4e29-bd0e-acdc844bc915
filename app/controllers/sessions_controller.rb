class SessionsController < ApplicationController
  def new
    # Login form
  end

  def create
    user = User.find_by(email: params[:email].downcase)
    
    if user&.authenticate(params[:password])
      if user.mfa_enabled?
        session[:pending_user_id] = user.id
        redirect_to mfa_verify_path
      else
        session[:user_id] = user.id
        redirect_to root_path, notice: "Welcome back!"
      end
    else
      flash.now[:alert] = "Invalid email or password"
      render :new, status: :unprocessable_entity
    end
  end

  def destroy
    session[:user_id] = nil
    session[:pending_user_id] = nil
    redirect_to login_path, notice: "Logged out successfully"
  end
end
