class MfaController < ApplicationController
  before_action :require_login
  before_action :require_pending_user, only: [:verify, :verify_token]

  def setup
    current_user.generate_mfa_secret
    current_user.save!
    @qr_code = current_user.mfa_qr_code
  end

  def enable
    if current_user.verify_mfa_token(params[:token])
      current_user.update!(mfa_enabled: true)
      redirect_to root_path, notice: "Two-factor authentication enabled successfully!"
    else
      flash.now[:alert] = "Invalid verification code. Please try again."
      @qr_code = current_user.mfa_qr_code
      render :setup, status: :unprocessable_entity
    end
  end

  def verify
    # Show MFA verification form
  end

  def verify_token
    user = User.find(session[:pending_user_id])
    
    if user.verify_mfa_token(params[:token])
      session[:user_id] = user.id
      session[:pending_user_id] = nil
      redirect_to root_path, notice: "Welcome back!"
    else
      flash.now[:alert] = "Invalid verification code. Please try again."
      render :verify, status: :unprocessable_entity
    end
  end

  def disable
    if current_user.authenticate(params[:password])
      current_user.update!(mfa_enabled: false, mfa_secret: nil)
      redirect_to root_path, notice: "Two-factor authentication disabled."
    else
      flash.now[:alert] = "Invalid password."
      render :disable, status: :unprocessable_entity
    end
  end

  private

  def require_pending_user
    redirect_to login_path unless session[:pending_user_id]
  end
end
