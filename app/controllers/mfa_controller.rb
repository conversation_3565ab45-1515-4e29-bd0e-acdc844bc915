class MfaController < ApplicationController
  before_action :require_login
  before_action :require_pending_user, only: [:verify, :verify_token]

  def setup
    current_user.generate_mfa_secret
    current_user.save!
    @qr_code = current_user.mfa_qr_code
  end

  def enable
    token = params[:token]&.strip

    if token.blank?
      flash.now[:alert] = "Please enter a verification code."
      @qr_code = current_user.mfa_qr_code
      render :setup, status: :unprocessable_entity
      return
    end

    if current_user.verify_mfa_token(token)
      current_user.update!(mfa_enabled: true)
      redirect_to root_path, notice: "Two-factor authentication enabled successfully!"
    else
      Rails.logger.debug "MFA verification failed for user #{current_user.id} with token: #{token}"
      flash.now[:alert] = "Invalid verification code. Please try again. Make sure you're entering the current 6-digit code from your authenticator app."
      @qr_code = current_user.mfa_qr_code
      render :setup, status: :unprocessable_entity
    end
  end

  def verify
    # Show MFA verification form
  end

  def verify_token
    user = User.find(session[:pending_user_id])

    if user.verify_mfa_token_for_login(params[:token])
      session[:user_id] = user.id
      session[:pending_user_id] = nil
      redirect_to root_path, notice: "Welcome back!"
    else
      flash.now[:alert] = "Invalid verification code. Please try again."
      render :verify, status: :unprocessable_entity
    end
  end

  def disable
    if current_user.authenticate(params[:password])
      current_user.update!(mfa_enabled: false, mfa_secret: nil)
      redirect_to root_path, notice: "Two-factor authentication disabled."
    else
      flash.now[:alert] = "Invalid password."
      render :disable, status: :unprocessable_entity
    end
  end

  private

  def require_pending_user
    redirect_to login_path unless session[:pending_user_id]
  end
end
