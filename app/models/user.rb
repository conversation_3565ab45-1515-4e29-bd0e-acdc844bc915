class User < ApplicationRecord
  has_secure_password

  validates :email, presence: true, uniqueness: { case_sensitive: false }
  validates :email, format: { with: URI::MailTo::EMAIL_REGEXP }

  before_save :downcase_email

  def generate_mfa_secret
    self.mfa_secret = ROTP::Base32.random
  end

  def mfa_qr_code
    return nil unless mfa_secret

    totp = ROTP::TOTP.new(mfa_secret, issuer: "BitePlan")
    provisioning_uri = totp.provisioning_uri(email)

    qrcode = RQRCode::QRCode.new(provisioning_uri)
    qrcode.as_svg(
      offset: 0,
      color: "000",
      shape_rendering: "crispEdges",
      module_size: 6,
      standalone: true
    )
  end

  def verify_mfa_token(token)
    return false unless mfa_secret && mfa_enabled?

    totp = ROTP::TOTP.new(mfa_secret)
    totp.verify(token, drift_behind: 15, drift_ahead: 15)
  end

  private

  def downcase_email
    self.email = email.downcase if email.present?
  end
end
