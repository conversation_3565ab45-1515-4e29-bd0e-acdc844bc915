require "base64"

class User < ApplicationRecord
  has_secure_password

  validates :email, presence: true, uniqueness: { case_sensitive: false }
  validates :email, format: { with: URI::MailTo::EMAIL_REGEXP }

  before_save :downcase_email

  def generate_mfa_secret
    self.mfa_secret = ROTP::Base32.random
  end

  def mfa_qr_code
    return nil unless mfa_secret

    totp = ROTP::TOTP.new(mfa_secret, issuer: "BitePlan")
    provisioning_uri = totp.provisioning_uri(email)

    qrcode = RQRCode::QRCode.new(provisioning_uri, level: :m)
    qrcode.as_svg(
      offset: 0,
      color: "000",
      shape_rendering: "crispEdges",
      module_size: 5,
      standalone: true
    )
  end

  def mfa_provisioning_uri
    return nil unless mfa_secret

    totp = ROTP::TOTP.new(mfa_secret, issuer: "BitePlan")
    totp.provisioning_uri(email)
  end

  def mfa_qr_code_png_data_uri
    return nil unless mfa_secret

    totp = ROTP::TOTP.new(mfa_secret, issuer: "BitePlan")
    provisioning_uri = totp.provisioning_uri(email)

    qrcode = RQRCode::QRCode.new(provisioning_uri, level: :m)
    png = qrcode.as_png(
      bit_depth: 1,
      border_modules: 4,
      color_mode: ChunkyPNG::COLOR_GRAYSCALE,
      color: "black",
      file: nil,
      fill: "white",
      module_px_size: 6,
      resize_exactly_to: false,
      resize_gte_to: false,
      size: 300
    )

    "data:image/png;base64,#{Base64.strict_encode64(png.to_s)}"
  end

  def verify_mfa_token(token)
    return false unless mfa_secret

    totp = ROTP::TOTP.new(mfa_secret)
    result = totp.verify(token, drift_behind: 15, drift_ahead: 15)
    !result.nil?
  end

  def verify_mfa_token_for_login(token)
    return false unless mfa_secret && mfa_enabled?

    totp = ROTP::TOTP.new(mfa_secret)
    result = totp.verify(token, drift_behind: 15, drift_ahead: 15)
    !result.nil?
  end

  private

  def downcase_email
    self.email = email.downcase if email.present?
  end
end
