<div class="auth-container">
  <div class="auth-card">
    <h1>Sign Up</h1>
    
    <%= form_with model: @user, local: true, class: "auth-form" do |form| %>
      <% if @user.errors.any? %>
        <div class="alert alert-danger">
          <ul>
            <% @user.errors.full_messages.each do |message| %>
              <li><%= message %></li>
            <% end %>
          </ul>
        </div>
      <% end %>

      <div class="form-group">
        <%= form.label :email, "Email" %>
        <%= form.email_field :email, required: true, class: "form-control" %>
      </div>

      <div class="form-group">
        <%= form.label :password, "Password" %>
        <%= form.password_field :password, required: true, class: "form-control" %>
      </div>

      <div class="form-group">
        <%= form.label :password_confirmation, "Confirm Password" %>
        <%= form.password_field :password_confirmation, required: true, class: "form-control" %>
      </div>

      <div class="form-actions">
        <%= form.submit "Sign Up", class: "btn btn-primary" %>
      </div>
    <% end %>

    <div class="auth-links">
      <p>Already have an account? <%= link_to "Sign in", login_path %></p>
    </div>
  </div>
</div>
