<div class="auth-container">
  <div class="auth-card">
    <h1>Set Up Two-Factor Authentication</h1>

    <div class="mfa-setup">
      <p><strong>Step 1:</strong> Install an authenticator app on your phone:</p>
      <ul>
        <li>Google Authenticator (iOS/Android)</li>
        <li>Authy (iOS/Android)</li>
        <li>Microsoft Authenticator (iOS/Android)</li>
      </ul>

      <p><strong>Step 2:</strong> Scan this QR code with your authenticator app:</p>

      <div class="qr-code">
        <%= @qr_code.html_safe %>
      </div>

      <p><em>If the QR code above doesn't work, try this alternative version:</em></p>
      <div class="qr-code">
        <img src="<%= current_user.mfa_qr_code_png_data_uri %>" alt="MFA QR Code" style="max-width: 200px; height: auto;">
      </div>

      <p><strong>Step 3:</strong> Or manually enter this information in your app:</p>
      <div class="manual-setup">
        <p><strong>Account:</strong> <%= current_user.email %></p>
        <p><strong>Secret Key:</strong> <code><%= current_user.mfa_secret %></code></p>
        <p><strong>Issuer:</strong> BitePlan</p>
      </div>

      <% if Rails.env.development? %>
        <details>
          <summary>Debug Info (Development Only)</summary>
          <p><strong>Provisioning URI:</strong></p>
          <code style="word-break: break-all;"><%= current_user.mfa_provisioning_uri %></code>
        </details>
      <% end %>

      <%= form_with url: mfa_enable_path, local: true, class: "auth-form" do |form| %>
        <div class="form-group">
          <%= form.label :token, "Enter the 6-digit code from your authenticator app:" %>
          <%= form.text_field :token, required: true, class: "form-control", maxlength: 6, pattern: "[0-9]{6}" %>
        </div>

        <div class="form-actions">
          <%= form.submit "Enable Two-Factor Authentication", class: "btn btn-primary" %>
        </div>
      <% end %>
    </div>
  </div>
</div>
