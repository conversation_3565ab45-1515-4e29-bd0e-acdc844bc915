Rails.application.routes.draw do
  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", as: :rails_health_check

  # Render dynamic PWA files from app/views/pwa/* (remember to link manifest in application.html.erb)
  # get "manifest" => "rails/pwa#manifest", as: :pwa_manifest
  # get "service-worker" => "rails/pwa#service_worker", as: :pwa_service_worker

  # Authentication routes
  get "login", to: "sessions#new"
  post "login", to: "sessions#create"
  delete "logout", to: "sessions#destroy"

  get "signup", to: "users#new"
  resources :users, only: [ :create ]

  # MFA routes
  get "mfa/setup", to: "mfa#setup", as: :mfa_setup
  post "mfa/enable", to: "mfa#enable", as: :mfa_enable
  get "mfa/verify", to: "mfa#verify", as: :mfa_verify
  post "mfa/verify", to: "mfa#verify_token", as: :mfa_verify_token
  delete "mfa/disable", to: "mfa#disable", as: :mfa_disable

  # Home page (protected)
  root "home#index"
end
